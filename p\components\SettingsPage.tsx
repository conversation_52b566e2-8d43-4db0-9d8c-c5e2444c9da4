import React from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Switch } from './ui/switch';
import { Label } from './ui/label';
import { Settings, Download, Upload, Moon, Sun, Palette } from 'lucide-react';
import { Divider } from './ui/divider';

export function SettingsPage() {
  const ContentPlaceholder = () => {
    return (
      <div className="relative h-full overflow-hidden rounded bg-gray-50 dark:bg-gray-800">
        <svg
          className="absolute inset-0 h-full w-full stroke-gray-200 dark:stroke-gray-700"
          fill="none"
        >
          <defs>
            <pattern
              id="pattern-settings"
              x="0"
              y="0"
              width="10"
              height="10"
              patternUnits="userSpaceOnUse"
            >
              <path d="M-3 13 15-5M-5 5l18-18M-1 21 17 3"></path>
            </pattern>
          </defs>
          <rect
            stroke="none"
            fill="url(#pattern-settings)"
            width="100%"
            height="100%"
          ></rect>
        </svg>
      </div>
    );
  };

  const settingsCategories = [
    {
      id: 'appearance',
      name: 'Appearance',
      description: 'Customize the look and feel of the application',
      icon: Palette,
      settings: [
        { name: 'Dark Mode', description: 'Switch between light and dark themes', icon: Moon, enabled: false },
        { name: 'Compact View', description: 'Show more items in the gallery view', icon: Sun, enabled: false }
      ]
    },
    {
      id: 'data',
      name: 'Data Management',
      description: 'Import and export your prompts and snippets',
      icon: Download,
      settings: [
        { name: 'Auto Backup', description: 'Automatically backup your data', icon: Upload, enabled: true },
        { name: 'Sync Settings', description: 'Sync across devices', icon: Download, enabled: false }
      ]
    },
    {
      id: 'preferences',
      name: 'Preferences',
      description: 'Configure your default settings',
      icon: Settings,
      settings: [
        { name: 'Auto-copy on click', description: 'Automatically copy content when clicking items', icon: Download, enabled: true },
        { name: 'Show toast notifications', description: 'Display notifications for actions', icon: Settings, enabled: true }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-50">
          Settings
        </h3>
        <span className="inline-flex size-6 items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-50">
          {settingsCategories.length}
        </span>
      </div>
      <Divider className="my-4" />

      {/* Settings Grid */}
      <dl className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {settingsCategories.map((category) => {
          const IconComponent = category.icon;
          return (
            <Card
              key={category.id}
              className="relative flex flex-col justify-between p-2 hover:shadow-lg transition-all duration-200"
            >
              <div className="relative h-28">
                <ContentPlaceholder />
                <span className="absolute inset-x-0 bottom-0 left-4 flex size-12 translate-y-1/2 items-center justify-center rounded-md border border-gray-200 bg-white p-1 shadow-sm dark:border-gray-800 dark:bg-[#090E1A]">
                  <IconComponent
                    className="size-5 text-blue-500 dark:text-blue-500"
                    aria-hidden={true}
                  />
                </span>
              </div>
              <div className="flex flex-1 flex-col px-2 pb-2 pt-8">
                <div className="flex-1">
                  <dt className="truncate text-sm font-medium text-gray-900 dark:text-gray-50">
                    {category.name}
                  </dt>
                  <dd className="mt-2 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                    {category.description}
                  </dd>
                  <div className="mt-3 space-y-2">
                    {category.settings.slice(0, 2).map((setting, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {setting.name}
                        </span>
                        <Switch
                          defaultChecked={setting.enabled}
                          className="scale-75"
                        />
                      </div>
                    ))}
                  </div>
                </div>
                <div className="mt-6 flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-500">
                    Settings
                  </span>
                  <span
                    className="inline-flex size-7 items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-700 dark:bg-gray-800 dark:text-gray-300"
                    aria-hidden={true}
                  >
                    {category.settings.length}
                  </span>
                </div>
              </div>
            </Card>
          );
        })}
      </dl>

      {/* Action Buttons */}
      <div className="mt-8 flex gap-4 max-w-md">
        <Button variant="outline" className="flex-1 border-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50">
          <Upload className="h-4 w-4 mr-2" />
          Import Data
        </Button>
        <Button variant="outline" className="flex-1 border-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50">
          <Download className="h-4 w-4 mr-2" />
          Export Data
        </Button>
      </div>
    </div>
  );
}