import React from 'react';
import { <PERSON><PERSON>, <PERSON>bar<PERSON>ontent, Sidebar<PERSON>ooter, SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarTrigger } from './ui/sidebar';
import { Button } from './ui/button';
import { Plus, Home, Star, Settings, Tag, Search } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
  currentPage: string;
  onPageChange: (page: string) => void;
  onAddNew: () => void;
}

export function Layout({ children, currentPage, onPageChange, onAddNew }: LayoutProps) {
  const navigationItems = [
    { id: 'gallery', label: 'All Items', icon: Home },
    { id: 'favorites', label: 'Favorites', icon: Star },
    { id: 'categories', label: 'Categories', icon: Tag },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <div className="min-h-screen flex w-full">
        <Sidebar className="border-r">
          <SidebarHeader className="p-6">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm">
                <Search className="h-4 w-4 text-white" />
              </div>
              <h1 className="font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Prompt Directory</h1>
            </div>
          </SidebarHeader>
          
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu>
                  {navigationItems.map((item) => (
                    <SidebarMenuItem key={item.id}>
                      <SidebarMenuButton
                        onClick={() => onPageChange(item.id)}
                        isActive={currentPage === item.id}
                        className="w-full justify-start"
                      >
                        <item.icon className="h-4 w-4" />
                        <span>{item.label}</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>

          <SidebarFooter className="p-4">
            <Button onClick={onAddNew} className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 shadow-sm">
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
          </SidebarFooter>
        </Sidebar>

        <div className="flex-1 flex flex-col">
          <header className="border-b bg-gradient-to-r from-blue-50/50 to-purple-50/50 p-4 flex items-center gap-4">
            <SidebarTrigger />
            <div className="flex-1" />
          </header>
          
          <main className="flex-1 p-6">
            {children}
          </main>
        </div>
      </div>
  );
}