import React, { useState } from 'react';
import { PromptItem, ViewMode } from '../types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Copy, Star, Edit, Trash2, Search, Grid3X3, List, Filter, FileText, Terminal, Code, Hash, Database, GitBranch, Container, Component, Bot, Settings } from 'lucide-react';
import { toast } from 'sonner';
import { Divider } from './ui/divider';

interface GalleryProps {
  items: PromptItem[];
  onEdit: (item: PromptItem) => void;
  onDelete: (id: string) => void;
  onToggleFavorite: (id: string) => void;
  onViewDetails: (item: PromptItem) => void;
}

export function Gallery({ items, onEdit, onDelete, onToggleFavorite, onViewDetails }: GalleryProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('gallery');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || item.type === filterType;
    const matchesCategory = filterCategory === 'all' || item.category === filterCategory;
    
    return matchesSearch && matchesType && matchesCategory;
  });

  const copyToClipboard = async (content: string, name: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast.success(`"${name}" copied to clipboard!`);
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'prompt': return 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border border-blue-200';
      case 'command': return 'bg-gradient-to-r from-green-100 to-green-50 text-green-800 border border-green-200';
      case 'snippet': return 'bg-gradient-to-r from-purple-100 to-purple-50 text-purple-800 border border-purple-200';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: any } = {
      FileText, Terminal, Code, Hash, Star, Database, GitBranch, Container, Component, Bot, Settings
    };
    return iconMap[iconName] || FileText;
  };

  const ContentPlaceholder = () => {
    return (
      <div className="relative h-full overflow-hidden rounded bg-gray-50 dark:bg-gray-800">
        <svg
          className="absolute inset-0 h-full w-full stroke-gray-200 dark:stroke-gray-700"
          fill="none"
        >
          <defs>
            <pattern
              id="pattern-2"
              x="0"
              y="0"
              width="10"
              height="10"
              patternUnits="userSpaceOnUse"
            >
              <path d="M-3 13 15-5M-5 5l18-18M-1 21 17 3"></path>
            </pattern>
          </defs>
          <rect
            stroke="none"
            fill="url(#pattern-2)"
            width="100%"
            height="100%"
          ></rect>
        </svg>
      </div>
    );
  };

  const categories = Array.from(new Set(items.map(item => item.category)));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <h1 className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Prompt Gallery</h1>
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'gallery' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('gallery')}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search prompts, commands, and snippets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="prompt">Prompts</SelectItem>
                <SelectItem value="command">Commands</SelectItem>
                <SelectItem value="snippet">Snippets</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="flex items-center space-x-2">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-50">
          Prompt Gallery
        </h3>
        <span className="inline-flex size-6 items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-50">
          {filteredItems.length}
        </span>
      </div>
      <Divider className="my-4" />

      {/* Gallery View */}
      {viewMode === 'gallery' && (
        <dl className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {filteredItems.map((item) => {
            const IconComponent = getIconComponent(item.icon);
            const formatDate = (date: Date) => {
              const now = new Date();
              const diffTime = Math.abs(now.getTime() - date.getTime());
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

              if (diffDays === 1) return '1 day ago';
              if (diffDays < 7) return `${diffDays} days ago`;
              if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
              return `${Math.ceil(diffDays / 30)} months ago`;
            };

            return (
              <Card
                key={item.id}
                className="relative flex flex-col justify-between p-2 hover:shadow-lg transition-all duration-200"
              >
                <div className="relative h-28">
                  <ContentPlaceholder />
                  <span className="absolute inset-x-0 bottom-0 left-4 flex size-12 translate-y-1/2 items-center justify-center rounded-md border border-gray-200 bg-white p-1 shadow-sm dark:border-gray-800 dark:bg-[#090E1A]">
                    <IconComponent
                      className="size-5 text-blue-500 dark:text-blue-500"
                      aria-hidden={true}
                    />
                  </span>
                </div>
                <div className="flex flex-1 flex-col px-2 pb-2 pt-8">
                  <div className="flex-1">
                    <dt className="truncate text-sm font-medium text-gray-900 dark:text-gray-50">
                      <button
                        onClick={() => onViewDetails(item)}
                        className="focus:outline-none text-left w-full"
                      >
                        <span className="absolute inset-0" aria-hidden={true} />
                        {item.name}
                      </button>
                    </dt>
                    <dd className="mt-2 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                      {item.description || item.content}
                    </dd>
                    <div className="mt-2 flex items-center gap-2">
                      <Badge variant="secondary" className={getTypeColor(item.type)}>
                        {item.type}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {item.category}
                      </Badge>
                    </div>
                  </div>
                  <div className="mt-6 flex items-center justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-500">
                      {formatDate(item.lastModified)}
                    </span>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          copyToClipboard(item.content, item.name);
                        }}
                        className="inline-flex size-7 items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors"
                        title="Copy to clipboard"
                      >
                        <Copy className="size-3" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onToggleFavorite(item.id);
                        }}
                        className="inline-flex size-7 items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors"
                        title={item.isFavorite ? "Remove from favorites" : "Add to favorites"}
                      >
                        <Star className={`size-3 ${item.isFavorite ? 'fill-current text-yellow-500' : ''}`} />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onEdit(item);
                        }}
                        className="inline-flex size-7 items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors"
                        title="Edit"
                      >
                        <Edit className="size-3" />
                      </button>
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
        </dl>
      )}

      {/* List View */}
      {viewMode === 'list' && (
        <div className="space-y-2">
          {filteredItems.map((item) => (
            <Card key={item.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-medium truncate">{item.name}</h3>
                    <Badge variant="secondary" className={getTypeColor(item.type)}>
                      {item.type}
                    </Badge>
                    <Badge variant="outline">{item.category}</Badge>
                    {item.isFavorite && <Star className="h-4 w-4 fill-current text-yellow-500" />}
                  </div>
                  <p className="text-muted-foreground line-clamp-2">
                    {item.description || item.content}
                  </p>
                </div>
                <div className="flex items-center gap-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(item.content, item.name)}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => onViewDetails(item)}>
                    View
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => onEdit(item)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No items found matching your criteria.</p>
        </div>
      )}
    </div>
  );
}