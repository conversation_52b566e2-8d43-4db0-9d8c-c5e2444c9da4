import React from 'react';
import { PromptItem } from '../types';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Copy, Edit, Star, Trash2, Calendar } from 'lucide-react';
import { toast } from 'sonner';

interface ItemDetailModalProps {
  item: PromptItem | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (item: PromptItem) => void;
  onDelete: (id: string) => void;
  onToggleFavorite: (id: string) => void;
}

export function ItemDetailModal({ item, isOpen, onClose, onEdit, onDelete, onToggleFavorite }: ItemDetailModalProps) {
  if (!item) return null;

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(item.content);
      toast.success(`"${item.name}" copied to clipboard!`);
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'prompt': return 'bg-blue-100 text-blue-800';
      case 'command': return 'bg-green-100 text-green-800';
      case 'snippet': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <DialogTitle className="flex items-center gap-3 mb-2">
                <span className="truncate">{item.name}</span>
                {item.isFavorite && <Star className="h-5 w-5 fill-current text-yellow-500 flex-shrink-0" />}
              </DialogTitle>
              <div className="flex flex-wrap items-center gap-2">
                <Badge variant="secondary" className={getTypeColor(item.type)}>
                  {item.type}
                </Badge>
                <Badge variant="outline">{item.category}</Badge>
                <div className="flex items-center gap-1 text-muted-foreground text-sm">
                  <Calendar className="h-3 w-3" />
                  <span>Added {formatDate(item.dateAdded)}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0 ml-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onToggleFavorite(item.id)}
              >
                <Star className={`h-4 w-4 ${item.isFavorite ? 'fill-current text-yellow-500' : ''}`} />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => onEdit(item)}>
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  onDelete(item.id);
                  onClose();
                }}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 min-h-0 space-y-4">
          {item.description && (
            <div>
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-muted-foreground">{item.description}</p>
            </div>
          )}

          <div className="flex-1 min-h-0">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">Content</h4>
              <Button onClick={copyToClipboard} size="sm">
                <Copy className="h-4 w-4 mr-2" />
                Copy to Clipboard
              </Button>
            </div>
            <div className="bg-muted p-4 rounded-lg h-full min-h-[200px] overflow-auto">
              <pre className="whitespace-pre-wrap font-mono text-sm leading-relaxed">
                {item.content}
              </pre>
            </div>
          </div>

          {item.lastModified.getTime() !== item.dateAdded.getTime() && (
            <div className="text-sm text-muted-foreground">
              Last modified: {formatDate(item.lastModified)}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}