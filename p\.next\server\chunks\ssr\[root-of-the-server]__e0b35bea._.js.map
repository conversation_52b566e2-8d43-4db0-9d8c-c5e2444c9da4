{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/app/page.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from 'react';\nimport { PromptItem } from '../types';\nimport { Layout } from './components/Layout';\nimport { Gallery } from './components/Gallery';\nimport { AddEditForm } from './components/AddEditForm';\nimport { ItemDetailModal } from './components/ItemDetailModal';\nimport { FavoritesPage } from './components/FavoritesPage';\nimport { CategoriesPage } from './components/CategoriesPage';\nimport { SettingsPage } from './components/SettingsPage';\nimport { SidebarProvider } from './components/ui/sidebar';\nimport { Toaster } from './components/ui/sonner';\nimport { mockPrompts } from './data/mockData';\n\ntype AppMode = 'gallery' | 'add' | 'edit' | 'favorites' | 'categories' | 'settings';\n\nexport default function App() {\n  const [items, setItems] = useState<PromptItem[]>(mockPrompts);\n  const [mode, setMode] = useState<AppMode>('gallery');\n  const [selectedItem, setSelectedItem] = useState<PromptItem | null>(null);\n  const [editingItem, setEditingItem] = useState<PromptItem | null>(null);\n\n  const addItem = (newItem: Omit<PromptItem, 'id' | 'dateAdded' | 'lastModified'>) => {\n    const item: PromptItem = {\n      ...newItem,\n      id: Date.now().toString(),\n      dateAdded: new Date(),\n      lastModified: new Date(),\n    };\n    setItems(prev => [item, ...prev]);\n    setMode('gallery');\n  };\n\n  const updateItem = (updatedItem: Omit<PromptItem, 'id' | 'dateAdded' | 'lastModified'>) => {\n    if (!editingItem) return;\n    \n    setItems(prev => prev.map(item => \n      item.id === editingItem.id \n        ? { \n            ...item, \n            ...updatedItem,\n            lastModified: new Date()\n          }\n        : item\n    ));\n    setEditingItem(null);\n    setMode('gallery');\n  };\n\n  const deleteItem = (id: string) => {\n    setItems(prev => prev.filter(item => item.id !== id));\n    setSelectedItem(null);\n  };\n\n  const toggleFavorite = (id: string) => {\n    setItems(prev => prev.map(item => \n      item.id === id ? { ...item, isFavorite: !item.isFavorite } : item\n    ));\n  };\n\n  const handleEdit = (item: PromptItem) => {\n    setEditingItem(item);\n    setMode('edit');\n  };\n\n  const handleViewDetails = (item: PromptItem) => {\n    setSelectedItem(item);\n  };\n\n  const handleAddNew = () => {\n    setEditingItem(null);\n    setMode('add');\n  };\n\n  const handleCancel = () => {\n    setEditingItem(null);\n    setMode('gallery');\n  };\n\n  const renderContent = () => {\n    switch (mode) {\n      case 'add':\n      case 'edit':\n        return (\n          <AddEditForm\n            item={editingItem}\n            onSave={editingItem ? updateItem : addItem}\n            onCancel={handleCancel}\n          />\n        );\n      case 'favorites':\n        return (\n          <FavoritesPage\n            items={items}\n            onEdit={handleEdit}\n            onDelete={deleteItem}\n            onToggleFavorite={toggleFavorite}\n            onViewDetails={handleViewDetails}\n          />\n        );\n      case 'categories':\n        return <CategoriesPage items={items} />;\n      case 'settings':\n        return <SettingsPage />;\n      default:\n        return (\n          <Gallery\n            items={items}\n            onEdit={handleEdit}\n            onDelete={deleteItem}\n            onToggleFavorite={toggleFavorite}\n            onViewDetails={handleViewDetails}\n          />\n        );\n    }\n  };\n\n  return (\n    <SidebarProvider>\n      <div className=\"min-h-screen w-full\">\n        <Layout \n          currentPage={mode}\n          onNavigate={setMode}\n          onAddNew={handleAddNew}\n        >\n          <div className=\"p-6\">\n            {renderContent()}\n          </div>\n        </Layout>\n        \n        {selectedItem && (\n          <ItemDetailModal\n            item={selectedItem}\n            onClose={() => setSelectedItem(null)}\n            onEdit={() => handleEdit(selectedItem)}\n            onDelete={() => deleteItem(selectedItem.id)}\n            onToggleFavorite={() => toggleFavorite(selectedItem.id)}\n          />\n        )}\n        \n        <Toaster position=\"top-right\" />\n      </div>\n    </SidebarProvider>\n  );\n}"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;;;;;;;;;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAElE,MAAM,UAAU,CAAC;QACf,MAAM,OAAmB;YACvB,GAAG,OAAO;YACV,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI;YACf,cAAc,IAAI;QACpB;QACA,SAAS,CAAA,OAAQ;gBAAC;mBAAS;aAAK;QAChC,QAAQ;IACV;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,aAAa;QAElB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;oBACE,GAAG,IAAI;oBACP,GAAG,WAAW;oBACd,cAAc,IAAI;gBACpB,IACA;QAEN,eAAe;QACf,QAAQ;IACV;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACjD,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,YAAY,CAAC,KAAK,UAAU;gBAAC,IAAI;IAEjE;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,QAAQ;IACV;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,QAAQ;IACV;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,QAAQ;IACV;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBACE,8OAAC;oBACC,MAAM;oBACN,QAAQ,cAAc,aAAa;oBACnC,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC;oBACC,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,kBAAkB;oBAClB,eAAe;;;;;;YAGrB,KAAK;gBACH,qBAAO,8OAAC;oBAAe,OAAO;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV;gBACE,qBACE,8OAAC;oBACC,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,kBAAkB;oBAClB,eAAe;;;;;;QAGvB;IACF;IAEA,qBACE,8OAAC;kBACC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,aAAa;oBACb,YAAY;oBACZ,UAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;gBAIJ,8BACC,8OAAC;oBACC,MAAM;oBACN,SAAS,IAAM,gBAAgB;oBAC/B,QAAQ,IAAM,WAAW;oBACzB,UAAU,IAAM,WAAW,aAAa,EAAE;oBAC1C,kBAAkB,IAAM,eAAe,aAAa,EAAE;;;;;;8BAI1D,8OAAC;oBAAQ,UAAS;;;;;;;;;;;;;;;;;AAI1B", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}