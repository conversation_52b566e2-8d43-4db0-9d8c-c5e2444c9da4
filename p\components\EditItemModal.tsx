import React, { useState, useEffect } from 'react';
import { PromptItem, ItemType } from '../types';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { Badge } from './ui/badge';
import { X, Save, FileText, Terminal, Code, Hash, Star, Database, GitBranch, Container, Component, Bot, Settings } from 'lucide-react';
import { mockCategories } from '../data/mockData';

interface EditItemModalProps {
  item: PromptItem | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (item: Omit<PromptItem, 'id' | 'dateAdded' | 'lastModified'>) => void;
}

const typeIcons = {
  prompt: FileText,
  command: Terminal,
  snippet: Code,
};

const availableIcons = [
  { name: 'FileText', icon: FileText, value: 'FileText' },
  { name: 'Terminal', icon: Terminal, value: 'Terminal' },
  { name: 'Code', icon: Code, value: 'Code' },
  { name: 'Hash', icon: Hash, value: 'Hash' },
  { name: 'Star', icon: Star, value: 'Star' },
  { name: 'Database', icon: Database, value: 'Database' },
  { name: 'GitBranch', icon: GitBranch, value: 'GitBranch' },
  { name: 'Container', icon: Container, value: 'Container' },
  { name: 'Component', icon: Component, value: 'Component' },
  { name: 'Bot', icon: Bot, value: 'Bot' },
  { name: 'Settings', icon: Settings, value: 'Settings' },
];

export function EditItemModal({ item, isOpen, onClose, onSave }: EditItemModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    type: 'prompt' as ItemType,
    content: '',
    description: '',
    category: '',
    icon: 'FileText',
    isFavorite: false,
  });

  useEffect(() => {
    if (item) {
      setFormData({
        name: item.name,
        type: item.type,
        content: item.content,
        description: item.description || '',
        category: item.category,
        icon: item.icon,
        isFavorite: item.isFavorite,
      });
    }
  }, [item]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  if (!item) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="p-1 rounded bg-gradient-to-br from-blue-500 to-purple-600">
              <Settings className="h-4 w-4 text-white" />
            </div>
            Edit Item
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter item name"
              required
            />
          </div>

          {/* Type */}
          <div className="space-y-2">
            <Label htmlFor="type">Type</Label>
            <Select value={formData.type} onValueChange={(value: ItemType) => setFormData({ ...formData, type: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="prompt">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Prompt
                  </div>
                </SelectItem>
                <SelectItem value="command">
                  <div className="flex items-center gap-2">
                    <Terminal className="h-4 w-4" />
                    Command
                  </div>
                </SelectItem>
                <SelectItem value="snippet">
                  <div className="flex items-center gap-2">
                    <Code className="h-4 w-4" />
                    Snippet
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {mockCategories.map((category) => (
                  <SelectItem key={category.id} value={category.name}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Icon */}
          <div className="space-y-2">
            <Label>Icon</Label>
            <div className="grid grid-cols-6 gap-2">
              {availableIcons.map((iconOption) => {
                const IconComponent = iconOption.icon;
                return (
                  <button
                    key={iconOption.value}
                    type="button"
                    onClick={() => setFormData({ ...formData, icon: iconOption.value })}
                    className={`p-3 rounded-lg border-2 transition-all ${
                      formData.icon === iconOption.value
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 hover:border-gray-300 dark:border-gray-700'
                    }`}
                  >
                    <IconComponent className="h-5 w-5 mx-auto" />
                  </button>
                );
              })}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Enter description (optional)"
              rows={3}
            />
          </div>

          {/* Content */}
          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
              placeholder="Enter the prompt, command, or code snippet"
              rows={8}
              required
            />
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button type="submit" className="flex-1">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
            <Button type="button" variant="outline" onClick={handleCancel}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
