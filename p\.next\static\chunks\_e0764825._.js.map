{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\";\n\nconst MOBILE_BREAKPOINT = 768;\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(\n    undefined,\n  );\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n    };\n    mql.addEventListener(\"change\", onChange);\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n    return () => mql.removeEventListener(\"change\", onChange);\n  }, []);\n\n  return !!isMobile;\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,WAAc,CAC5C;IAGF,6JAAA,CAAA,YAAe;iCAAC;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,AAAC,eAAoC,OAAtB,oBAAoB,GAAE;YACnE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAhBgB", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"./utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background text-foreground hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9 rounded-md\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"./utils\";\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border px-3 py-1 text-base bg-input-background transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8bACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/separator.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\";\n\nimport { cn } from \"./utils\";\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator-root\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Separator };\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\";\nimport { X } from \"lucide-react\";\n\nimport { cn } from \"./utils\";\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />;\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />;\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />;\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />;\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\";\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className,\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <X className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  );\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"./utils\";\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  );\n}\n\nexport { Skeleton };\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\n\nimport { cn } from \"./utils\";\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  );\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  );\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />;\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className,\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  );\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD,GALhC;IAMtB,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { VariantProps, cva } from \"class-variance-authority\";\nimport { PanelLeft } from \"lucide-react\";\n\nimport { useIsMobile } from \"./use-mobile\";\nimport { cn } from \"./utils\";\nimport { Button } from \"./button\";\nimport { Input } from \"./input\";\nimport { Separator } from \"./separator\";\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"./sheet\";\nimport { Skeleton } from \"./skeleton\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"./tooltip\";\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\nconst SIDEBAR_WIDTH = \"16rem\";\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\";\n  open: boolean;\n  setOpen: (open: boolean) => void;\n  openMobile: boolean;\n  setOpenMobile: (open: boolean) => void;\n  isMobile: boolean;\n  toggleSidebar: () => void;\n};\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext);\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\n  }\n\n  return context;\n}\n\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  defaultOpen?: boolean;\n  open?: boolean;\n  onOpenChange?: (open: boolean) => void;\n}) {\n  const isMobile = useIsMobile();\n  const [openMobile, setOpenMobile] = React.useState(false);\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen);\n  const open = openProp ?? _open;\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === \"function\" ? value(open) : value;\n      if (setOpenProp) {\n        setOpenProp(openState);\n      } else {\n        _setOpen(openState);\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\n    },\n    [setOpenProp, open],\n  );\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\n  }, [isMobile, setOpen, setOpenMobile]);\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n        (event.metaKey || event.ctrlKey)\n      ) {\n        event.preventDefault();\n        toggleSidebar();\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [toggleSidebar]);\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? \"expanded\" : \"collapsed\";\n\n  const contextValue = React.useMemo<SidebarContextProps>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar],\n  );\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          data-slot=\"sidebar-wrapper\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH,\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\n            className,\n          )}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  );\n}\n\nfunction Sidebar({\n  side = \"left\",\n  variant = \"sidebar\",\n  collapsible = \"offcanvas\",\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  side?: \"left\" | \"right\";\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\n}) {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\n\n  if (collapsible === \"none\") {\n    return (\n      <div\n        data-slot=\"sidebar\"\n        className={cn(\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\n          className,\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar\"\n          data-mobile=\"true\"\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <SheetHeader className=\"sr-only\">\n            <SheetTitle>Sidebar</SheetTitle>\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n          </SheetHeader>\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\n        </SheetContent>\n      </Sheet>\n    );\n  }\n\n  return (\n    <div\n      className=\"group peer text-sidebar-foreground hidden md:block\"\n      data-state={state}\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n      data-variant={variant}\n      data-side={side}\n      data-slot=\"sidebar\"\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        data-slot=\"sidebar-gap\"\n        className={cn(\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\n          \"group-data-[collapsible=offcanvas]:w-0\",\n          \"group-data-[side=right]:rotate-180\",\n          variant === \"floating\" || variant === \"inset\"\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\",\n        )}\n      />\n      <div\n        data-slot=\"sidebar-container\"\n        className={cn(\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\n          side === \"left\"\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n          // Adjust the padding for floating and inset variants.\n          variant === \"floating\" || variant === \"inset\"\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n          className,\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar-inner\"\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { toggleSidebar } = useSidebar();\n\n  return (\n    <Button\n      data-sidebar=\"trigger\"\n      data-slot=\"sidebar-trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"size-7\", className)}\n      onClick={(event) => {\n        onClick?.(event);\n        toggleSidebar();\n      }}\n      {...props}\n    >\n      <PanelLeft />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  );\n}\n\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\n  const { toggleSidebar } = useSidebar();\n\n  return (\n    <button\n      data-sidebar=\"rail\"\n      data-slot=\"sidebar-rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\n  return (\n    <main\n      data-slot=\"sidebar-inset\"\n      className={cn(\n        \"bg-background relative flex w-full flex-1 flex-col\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return (\n    <Input\n      data-slot=\"sidebar-input\"\n      data-sidebar=\"input\"\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-header\"\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-footer\"\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return (\n    <Separator\n      data-slot=\"sidebar-separator\"\n      data-sidebar=\"separator\"\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-content\"\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group\"\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"div\";\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-label\"\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-action\"\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group-content\"\n      data-sidebar=\"group-content\"\n      className={cn(\"w-full text-sm\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu\"\n      data-sidebar=\"menu\"\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-item\"\n      data-sidebar=\"menu-item\"\n      className={cn(\"group/menu-item relative\", className)}\n      {...props}\n    />\n  );\n}\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = \"default\",\n  size = \"default\",\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean;\n  isActive?: boolean;\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : \"button\";\n  const { isMobile, state } = useSidebar();\n\n  const button = (\n    <Comp\n      data-slot=\"sidebar-menu-button\"\n      data-sidebar=\"menu-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  );\n\n  if (!tooltip) {\n    return button;\n  }\n\n  if (typeof tooltip === \"string\") {\n    tooltip = {\n      children: tooltip,\n    };\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent\n        side=\"right\"\n        align=\"center\"\n        hidden={state !== \"collapsed\" || isMobile}\n        {...tooltip}\n      />\n    </Tooltip>\n  );\n}\n\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean;\n  showOnHover?: boolean;\n}) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-action\"\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-menu-badge\"\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  showIcon?: boolean;\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`;\n  }, []);\n\n  return (\n    <div\n      data-slot=\"sidebar-menu-skeleton\"\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  );\n}\n\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu-sub\"\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-sub-item\"\n      data-sidebar=\"menu-sub-item\"\n      className={cn(\"group/menu-sub-item relative\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = \"md\",\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean;\n  size?: \"sm\" | \"md\";\n  isActive?: boolean;\n}) {\n  const Comp = asChild ? Slot : \"a\";\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-sub-button\"\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,6JAAA,CAAA,gBAAmB,CAA6B;AAEvE,SAAS;;IACP,MAAM,UAAU,6JAAA,CAAA,aAAgB,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,KAYxB;QAZwB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ,GAZwB;;IAavB,MAAM,WAAW,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,WAAc,CAAC;IACzC,MAAM,OAAO,qBAAA,sBAAA,WAAY;IACzB,MAAM,UAAU,6JAAA,CAAA,cAAiB;gDAC/B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,AAAC,GAAyB,OAAvB,qBAAoB,KAAiC,OAA9B,WAAU,sBAA2C,OAAvB;QAC5E;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,6JAAA,CAAA,cAAiB;sDAAC;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,6JAAA,CAAA,YAAe;qCAAC;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,6JAAA,CAAA,UAAa;iDAChC,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,+HAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU,oIAAA,CAAA,cAAW;;;KAbrB;AAkGT,SAAS,QAAQ,KAWhB;QAXgB,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ,GAXgB;;IAYf,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,6HAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,6HAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,6HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,6HAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,6HAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,KAIc;QAJd,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC,GAJd;;IAKtB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,8HAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,oBAAA,8BAAA,QAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,YAAS;;;;;0BACV,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,KAAuD;QAAvD,EAAE,SAAS,EAAE,GAAG,OAAuC,GAAvD;;IACnB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,KAAqD;QAArD,EAAE,SAAS,EAAE,GAAG,OAAqC,GAArD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAGe;QAHf,EACpB,SAAS,EACT,GAAG,OACgC,GAHf;IAIpB,qBACE,6LAAC,6HAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,KAGe;QAHf,EACxB,SAAS,EACT,GAAG,OACoC,GAHf;IAIxB,qBACE,6LAAC,iIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,KAI2B;QAJ3B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD,GAJ3B;IAKzB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,KAI6B;QAJ7B,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD,GAJ7B;IAK1B,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,KAGC;QAHD,EAC3B,SAAS,EACT,GAAG,OACyB,GAHD;IAI3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,KAYuB;QAZvB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C,GAZvB;;IAazB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAO;;0BACN,6LAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,+HAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;IAhDS;;QAcqB;;;OAdrB;AAkDT,SAAS,kBAAkB,KAQ1B;QAR0B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ,GAR0B;IASzB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS;AAgCT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OApBS;AAsBT,SAAS,oBAAoB,KAM5B;QAN4B,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ,GAN4B;;IAO3B,kCAAkC;IAClC,MAAM,QAAQ,6JAAA,CAAA,UAAa;8CAAC;YAC1B,OAAO,AAAC,GAAsC,OAApC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IAAG;QAChD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,KAGC;QAHD,EAC1B,SAAS,EACT,GAAG,OACwB,GAHD;IAI1B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,KAU7B;QAV6B,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ,GAV6B;IAW5B,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON><PERSON>, <PERSON>bar<PERSON>ontent, Sidebar<PERSON>ooter, SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarTrigger } from './ui/sidebar';\nimport { Button } from './ui/button';\nimport { Plus, Home, Star, Settings, Tag, Search } from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  currentPage: string;\n  onPageChange: (page: string) => void;\n  onAddNew: () => void;\n}\n\nexport function Layout({ children, currentPage, onPageChange, onAddNew }: LayoutProps) {\n  const navigationItems = [\n    { id: 'gallery', label: 'All Items', icon: Home },\n    { id: 'favorites', label: 'Favorites', icon: Star },\n    { id: 'categories', label: 'Categories', icon: Tag },\n    { id: 'settings', label: 'Settings', icon: Settings },\n  ];\n\n  return (\n    <div className=\"min-h-screen flex w-full\">\n        <Sidebar className=\"border-r\">\n          <SidebarHeader className=\"p-6\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm\">\n                <Search className=\"h-4 w-4 text-white\" />\n              </div>\n              <h1 className=\"font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">Prompt Directory</h1>\n            </div>\n          </SidebarHeader>\n          \n          <SidebarContent>\n            <SidebarGroup>\n              <SidebarGroupContent>\n                <SidebarMenu>\n                  {navigationItems.map((item) => (\n                    <SidebarMenuItem key={item.id}>\n                      <SidebarMenuButton\n                        onClick={() => onPageChange(item.id)}\n                        isActive={currentPage === item.id}\n                        className=\"w-full justify-start\"\n                      >\n                        <item.icon className=\"h-4 w-4\" />\n                        <span>{item.label}</span>\n                      </SidebarMenuButton>\n                    </SidebarMenuItem>\n                  ))}\n                </SidebarMenu>\n              </SidebarGroupContent>\n            </SidebarGroup>\n          </SidebarContent>\n\n          <SidebarFooter className=\"p-4\">\n            <Button onClick={onAddNew} className=\"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 shadow-sm\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add New\n            </Button>\n          </SidebarFooter>\n        </Sidebar>\n\n        <div className=\"flex-1 flex flex-col\">\n          <header className=\"border-b bg-gradient-to-r from-blue-50/50 to-purple-50/50 p-4 flex items-center gap-4\">\n            <SidebarTrigger />\n            <div className=\"flex-1\" />\n          </header>\n          \n          <main className=\"flex-1 p-6\">\n            {children}\n          </main>\n        </div>\n      </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AASO,SAAS,OAAO,KAA8D;QAA9D,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAe,GAA9D;IACrB,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAW,OAAO;YAAa,MAAM,sMAAA,CAAA,OAAI;QAAC;QAChD;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,qMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM,mMAAA,CAAA,MAAG;QAAC;QACnD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACrD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACX,6LAAC,+HAAA,CAAA,UAAO;gBAAC,WAAU;;kCACjB,6LAAC,+HAAA,CAAA,gBAAa;wBAAC,WAAU;kCACvB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAG,WAAU;8CAAyF;;;;;;;;;;;;;;;;;kCAI3G,6LAAC,+HAAA,CAAA,iBAAc;kCACb,cAAA,6LAAC,+HAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,+HAAA,CAAA,sBAAmB;0CAClB,cAAA,6LAAC,+HAAA,CAAA,cAAW;8CACT,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+HAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,+HAAA,CAAA,oBAAiB;gDAChB,SAAS,IAAM,aAAa,KAAK,EAAE;gDACnC,UAAU,gBAAgB,KAAK,EAAE;gDACjC,WAAU;;kEAEV,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,6LAAC;kEAAM,KAAK,KAAK;;;;;;;;;;;;2CAPC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;kCAgBvC,6LAAC,+HAAA,CAAA,gBAAa;wBAAC,WAAU;kCACvB,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAU,WAAU;;8CACnC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,+HAAA,CAAA,iBAAc;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKb;KA7DgB", "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"./utils\";\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pt-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <h4\n      data-slot=\"card-title\"\n      className={cn(\"leading-none\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <p\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6 [&:last-child]:pb-6\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 pb-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,mKACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"./utils\";\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  },\n);\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\";\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1716, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\nimport {\n  CheckIcon,\n  ChevronDownIcon,\n  ChevronUpIcon,\n} from \"lucide-react\";\n\nimport { cn } from \"./utils\";\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\";\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-input-background px-3 py-2 text-sm whitespace-nowrap transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className,\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  );\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className,\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\",\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  );\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className,\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  );\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className,\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  );\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className,\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  );\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAMA;AAVA;;;;;AAYA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4yBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/Gallery.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { PromptItem, ViewMode } from '../types';\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';\nimport { Button } from './ui/button';\nimport { Badge } from './ui/badge';\nimport { Input } from './ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';\nimport { Copy, Star, Edit, Trash2, Search, Grid3X3, List, Filter, FileText, Terminal, Code, Hash, Database, GitBranch, Container, Component, Bot, Settings } from 'lucide-react';\nimport { toast } from 'sonner';\n\ninterface GalleryProps {\n  items: PromptItem[];\n  onEdit: (item: PromptItem) => void;\n  onDelete: (id: string) => void;\n  onToggleFavorite: (id: string) => void;\n  onViewDetails: (item: PromptItem) => void;\n}\n\nexport function Gallery({ items, onEdit, onDelete, onToggleFavorite, onViewDetails }: GalleryProps) {\n  const [viewMode, setViewMode] = useState<ViewMode>('gallery');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState<string>('all');\n  const [filterCategory, setFilterCategory] = useState<string>('all');\n\n  const filteredItems = items.filter(item => {\n    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         item.content.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         item.description?.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filterType === 'all' || item.type === filterType;\n    const matchesCategory = filterCategory === 'all' || item.category === filterCategory;\n    \n    return matchesSearch && matchesType && matchesCategory;\n  });\n\n  const copyToClipboard = async (content: string, name: string) => {\n    try {\n      await navigator.clipboard.writeText(content);\n      toast.success(`\"${name}\" copied to clipboard!`);\n    } catch (err) {\n      toast.error('Failed to copy to clipboard');\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'prompt': return 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border border-blue-200';\n      case 'command': return 'bg-gradient-to-r from-green-100 to-green-50 text-green-800 border border-green-200';\n      case 'snippet': return 'bg-gradient-to-r from-purple-100 to-purple-50 text-purple-800 border border-purple-200';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getIconComponent = (iconName: string) => {\n    const iconMap: { [key: string]: any } = {\n      FileText, Terminal, Code, Hash, Star, Database, GitBranch, Container, Component, Bot, Settings\n    };\n    return iconMap[iconName] || FileText;\n  };\n\n  const categories = Array.from(new Set(items.map(item => item.category)));\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col gap-4\">\n        <div className=\"flex items-center justify-between\">\n          <h1 className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">Prompt Gallery</h1>\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant={viewMode === 'gallery' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setViewMode('gallery')}\n            >\n              <Grid3X3 className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant={viewMode === 'list' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setViewMode('list')}\n            >\n              <List className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Search prompts, commands, and snippets...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n          <div className=\"flex gap-2\">\n            <Select value={filterType} onValueChange={setFilterType}>\n              <SelectTrigger className=\"w-32\">\n                <SelectValue placeholder=\"Type\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Types</SelectItem>\n                <SelectItem value=\"prompt\">Prompts</SelectItem>\n                <SelectItem value=\"command\">Commands</SelectItem>\n                <SelectItem value=\"snippet\">Snippets</SelectItem>\n              </SelectContent>\n            </Select>\n            <Select value={filterCategory} onValueChange={setFilterCategory}>\n              <SelectTrigger className=\"w-40\">\n                <SelectValue placeholder=\"Category\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Categories</SelectItem>\n                {categories.map(category => (\n                  <SelectItem key={category} value={category}>{category}</SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n        </div>\n      </div>\n\n      {/* Results */}\n      <div className=\"flex items-center justify-between\">\n        <p className=\"text-muted-foreground\">\n          {filteredItems.length} {filteredItems.length === 1 ? 'item' : 'items'} found\n        </p>\n      </div>\n\n      {/* Gallery View */}\n      {viewMode === 'gallery' && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredItems.map((item) => (\n            <Card key={item.id} className=\"group shadow-sm border hover:shadow-lg transition-all duration-200 hover:border-blue-200\">\n              <CardHeader className=\"space-y-2\">\n                <div className=\"flex items-start justify-between\">\n                  <CardTitle className=\"line-clamp-2\">{item.name}</CardTitle>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => onToggleFavorite(item.id)}\n                    className=\"opacity-0 group-hover:opacity-100 transition-opacity\"\n                  >\n                    <Star className={`h-4 w-4 ${item.isFavorite ? 'fill-current text-yellow-500' : ''}`} />\n                  </Button>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"p-1 rounded bg-gradient-to-br from-blue-500 to-purple-600\">\n                      {(() => {\n                        const IconComponent = getIconComponent(item.icon);\n                        return <IconComponent className=\"h-3 w-3 text-white\" />;\n                      })()}\n                    </div>\n                    <Badge variant=\"secondary\" className={getTypeColor(item.type)}>\n                      {item.type}\n                    </Badge>\n                  </div>\n                  <Badge variant=\"outline\">{item.category}</Badge>\n                </div>\n              </CardHeader>\n              \n              <CardContent>\n                <CardDescription className=\"line-clamp-3\">\n                  {item.description || item.content}\n                </CardDescription>\n              </CardContent>\n              \n              <CardFooter className=\"flex gap-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => copyToClipboard(item.content, item.name)}\n                  className=\"flex-1 border-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50\"\n                >\n                  <Copy className=\"h-4 w-4 mr-2\" />\n                  Copy\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => onViewDetails(item)}\n                >\n                  View\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => onEdit(item)}\n                >\n                  <Edit className=\"h-4 w-4\" />\n                </Button>\n              </CardFooter>\n            </Card>\n          ))}\n        </div>\n      )}\n\n      {/* List View */}\n      {viewMode === 'list' && (\n        <div className=\"space-y-2\">\n          {filteredItems.map((item) => (\n            <Card key={item.id} className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center gap-3 mb-2\">\n                    <h3 className=\"font-medium truncate\">{item.name}</h3>\n                    <Badge variant=\"secondary\" className={getTypeColor(item.type)}>\n                      {item.type}\n                    </Badge>\n                    <Badge variant=\"outline\">{item.category}</Badge>\n                    {item.isFavorite && <Star className=\"h-4 w-4 fill-current text-yellow-500\" />}\n                  </div>\n                  <p className=\"text-muted-foreground line-clamp-2\">\n                    {item.description || item.content}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-2 ml-4\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => copyToClipboard(item.content, item.name)}\n                  >\n                    <Copy className=\"h-4 w-4 mr-2\" />\n                    Copy\n                  </Button>\n                  <Button variant=\"ghost\" size=\"sm\" onClick={() => onViewDetails(item)}>\n                    View\n                  </Button>\n                  <Button variant=\"ghost\" size=\"sm\" onClick={() => onEdit(item)}>\n                    <Edit className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n            </Card>\n          ))}\n        </div>\n      )}\n\n      {filteredItems.length === 0 && (\n        <div className=\"text-center py-12\">\n          <p className=\"text-muted-foreground\">No items found matching your criteria.</p>\n        </div>\n      )}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;;;;;AAUO,SAAS,QAAQ,KAA0E;QAA1E,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,aAAa,EAAgB,GAA1E;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;YAGZ;QAFrB,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SAC1D,oBAAA,KAAK,WAAW,cAAhB,wCAAA,kBAAkB,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACpF,MAAM,cAAc,eAAe,SAAS,KAAK,IAAI,KAAK;QAC1D,MAAM,kBAAkB,mBAAmB,SAAS,KAAK,QAAQ,KAAK;QAEtE,OAAO,iBAAiB,eAAe;IACzC;IAEA,MAAM,kBAAkB,OAAO,SAAiB;QAC9C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,IAAQ,OAAL,MAAK;QACzB,EAAE,OAAO,KAAK;YACZ,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAkC;YACtC,UAAA,iNAAA,CAAA,WAAQ;YAAE,UAAA,6MAAA,CAAA,WAAQ;YAAE,MAAA,qMAAA,CAAA,OAAI;YAAE,MAAA,qMAAA,CAAA,OAAI;YAAE,MAAA,qMAAA,CAAA,OAAI;YAAE,UAAA,6MAAA,CAAA,WAAQ;YAAE,WAAA,mNAAA,CAAA,YAAS;YAAE,WAAA,+MAAA,CAAA,YAAS;YAAE,WAAA,+MAAA,CAAA,YAAS;YAAE,KAAA,mMAAA,CAAA,MAAG;YAAE,UAAA,6MAAA,CAAA,WAAQ;QAChG;QACA,OAAO,OAAO,CAAC,SAAS,IAAI,iNAAA,CAAA,WAAQ;IACtC;IAEA,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAErE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6E;;;;;;0CAC3F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS,aAAa,YAAY,YAAY;wCAC9C,MAAK;wCACL,SAAS,IAAM,YAAY;kDAE3B,cAAA,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;kDAE3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,6HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAGd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,6LAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;;kEACZ,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAGhC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAgB,eAAe;;0DAC5C,6LAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;;kEACZ,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;oDACvB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,8HAAA,CAAA,aAAU;4DAAgB,OAAO;sEAAW;2DAA5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBACV,cAAc,MAAM;wBAAC;wBAAE,cAAc,MAAM,KAAK,IAAI,SAAS;wBAAQ;;;;;;;;;;;;YAKzE,aAAa,2BACZ,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,4HAAA,CAAA,OAAI;wBAAe,WAAU;;0CAC5B,6LAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAgB,KAAK,IAAI;;;;;;0DAC9C,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,iBAAiB,KAAK,EAAE;gDACvC,WAAU;0DAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAW,AAAC,WAAgE,OAAtD,KAAK,UAAU,GAAG,iCAAiC;;;;;;;;;;;;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAC;4DACA,MAAM,gBAAgB,iBAAiB,KAAK,IAAI;4DAChD,qBAAO,6LAAC;gEAAc,WAAU;;;;;;wDAClC,CAAC;;;;;;kEAEH,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAW,aAAa,KAAK,IAAI;kEACzD,KAAK,IAAI;;;;;;;;;;;;0DAGd,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW,KAAK,QAAQ;;;;;;;;;;;;;;;;;;0CAI3C,6LAAC,4HAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,4HAAA,CAAA,kBAAe;oCAAC,WAAU;8CACxB,KAAK,WAAW,IAAI,KAAK,OAAO;;;;;;;;;;;0CAIrC,6LAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB,KAAK,OAAO,EAAE,KAAK,IAAI;wCACtD,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO;kDAEtB,cAAA,6LAAC,8MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;uBAzDX,KAAK,EAAE;;;;;;;;;;YAkEvB,aAAa,wBACZ,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,4HAAA,CAAA,OAAI;wBAAe,WAAU;kCAC5B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DAC/C,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAW,aAAa,KAAK,IAAI;8DACzD,KAAK,IAAI;;;;;;8DAEZ,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,KAAK,QAAQ;;;;;;gDACtC,KAAK,UAAU,kBAAI,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;sDAEtC,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW,IAAI,KAAK,OAAO;;;;;;;;;;;;8CAGrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,gBAAgB,KAAK,OAAO,EAAE,KAAK,IAAI;;8DAEtD,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,SAAS,IAAM,cAAc;sDAAO;;;;;;sDAGtE,6LAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,SAAS,IAAM,OAAO;sDACtD,cAAA,6LAAC,8MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uBA5Bb,KAAK,EAAE;;;;;;;;;;YAqCvB,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAK/C;GArOgB;KAAA", "debugId": null}}, {"offset": {"line": 2659, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"./utils\";\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"resize-none border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-input-background px-3 py-2 text-base transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Textarea };\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8cACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 2691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\n\nimport { cn } from \"./utils\";\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/data/mockData.ts"], "sourcesContent": ["import { PromptItem, Category } from '../types';\n\nexport const mockCategories: Category[] = [\n  { id: '1', name: 'AI Prompts', icon: 'Bo<PERSON>', color: '#3b82f6' },\n  { id: '2', name: 'Git Commands', icon: 'GitBranch', color: '#10b981' },\n  { id: '3', name: 'Docker', icon: 'Container', color: '#06b6d4' },\n  { id: '4', name: 'JavaScript', icon: 'Code', color: '#f59e0b' },\n  { id: '5', name: 'React', icon: 'Component', color: '#8b5cf6' },\n  { id: '6', name: 'Database', icon: 'Database', color: '#ef4444' },\n];\n\nexport const mockPrompts: PromptItem[] = [\n  {\n    id: '1',\n    name: 'Code Review Assistant',\n    type: 'prompt',\n    content: 'You are an expert code reviewer. Please review the following code and provide constructive feedback on:\\n1. Code quality and best practices\\n2. Performance optimizations\\n3. Security considerations\\n4. Maintainability improvements\\n\\nCode to review:\\n[PASTE CODE HERE]',\n    description: 'AI prompt for comprehensive code reviews',\n    category: 'AI Prompts',\n    icon: 'FileText',\n    isFavorite: true,\n    dateAdded: new Date('2024-01-15'),\n    lastModified: new Date('2024-01-20'),\n  },\n  {\n    id: '2',\n    name: 'Git Rebase Interactive',\n    type: 'command',\n    content: 'git rebase -i HEAD~3',\n    description: 'Interactive rebase for the last 3 commits',\n    category: 'Git Commands',\n    icon: 'GitBranch',\n    isFavorite: false,\n    dateAdded: new Date('2024-01-10'),\n    lastModified: new Date('2024-01-10'),\n  },\n  {\n    id: '3',\n    name: 'Docker Compose Dev Setup',\n    type: 'snippet',\n    content: `version: '3.8'\nservices:\n  app:\n    build: .\n    ports:\n      - \"3000:3000\"\n    volumes:\n      - .:/app\n      - /app/node_modules\n    environment:\n      - NODE_ENV=development\n  db:\n    image: postgres:13\n    environment:\n      - POSTGRES_DB=myapp\n      - POSTGRES_USER=user\n      - POSTGRES_PASSWORD=password\n    ports:\n      - \"5432:5432\"`,\n    description: 'Basic Docker Compose setup for development',\n    category: 'Docker',\n    icon: 'Container',\n    isFavorite: true,\n    dateAdded: new Date('2024-01-12'),\n    lastModified: new Date('2024-01-18'),\n  },\n  {\n    id: '4',\n    name: 'React useEffect Cleanup',\n    type: 'snippet',\n    content: `useEffect(() => {\n  const handleResize = () => {\n    setWindowSize({\n      width: window.innerWidth,\n      height: window.innerHeight,\n    });\n  };\n\n  window.addEventListener('resize', handleResize);\n\n  return () => {\n    window.removeEventListener('resize', handleResize);\n  };\n}, []);`,\n    description: 'Proper cleanup pattern for useEffect with event listeners',\n    category: 'React',\n    icon: 'Component',\n    isFavorite: false,\n    dateAdded: new Date('2024-01-08'),\n    lastModified: new Date('2024-01-08'),\n  },\n  {\n    id: '5',\n    name: 'API Documentation Generator',\n    type: 'prompt',\n    content: 'Generate comprehensive API documentation for the following endpoint. Include:\\n1. Endpoint description and purpose\\n2. HTTP method and URL\\n3. Request parameters (path, query, body)\\n4. Response format and status codes\\n5. Error handling\\n6. Example requests and responses\\n\\nEndpoint details:\\n[PASTE ENDPOINT INFO HERE]',\n    description: 'Generate detailed API documentation',\n    category: 'AI Prompts',\n    icon: 'FileText',\n    isFavorite: true,\n    dateAdded: new Date('2024-01-14'),\n    lastModified: new Date('2024-01-19'),\n  },\n  {\n    id: '6',\n    name: 'PostgreSQL Query Performance',\n    type: 'command',\n    content: 'EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM users WHERE email = $1;',\n    description: 'Analyze query performance with detailed execution plan',\n    category: 'Database',\n    icon: 'Database',\n    isFavorite: false,\n    dateAdded: new Date('2024-01-11'),\n    lastModified: new Date('2024-01-16'),\n  },\n];"], "names": [], "mappings": ";;;;AAEO,MAAM,iBAA6B;IACxC;QAAE,IAAI;QAAK,MAAM;QAAc,MAAM;QAAO,OAAO;IAAU;IAC7D;QAAE,IAAI;QAAK,MAAM;QAAgB,MAAM;QAAa,OAAO;IAAU;IACrE;QAAE,IAAI;QAAK,MAAM;QAAU,MAAM;QAAa,OAAO;IAAU;IAC/D;QAAE,IAAI;QAAK,MAAM;QAAc,MAAM;QAAQ,OAAO;IAAU;IAC9D;QAAE,IAAI;QAAK,MAAM;QAAS,MAAM;QAAa,OAAO;IAAU;IAC9D;QAAE,IAAI;QAAK,MAAM;QAAY,MAAM;QAAY,OAAO;IAAU;CACjE;AAEM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,aAAa;QACb,UAAU;QACV,MAAM;QACN,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,cAAc,IAAI,KAAK;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,aAAa;QACb,UAAU;QACV,MAAM;QACN,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,cAAc,IAAI,KAAK;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAU;QAmBV,aAAa;QACb,UAAU;QACV,MAAM;QACN,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,cAAc,IAAI,KAAK;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAU;QAcV,aAAa;QACb,UAAU;QACV,MAAM;QACN,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,cAAc,IAAI,KAAK;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,aAAa;QACb,UAAU;QACV,MAAM;QACN,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,cAAc,IAAI,KAAK;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,aAAa;QACb,UAAU;QACV,MAAM;QACN,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,cAAc,IAAI,KAAK;IACzB;CACD", "debugId": null}}, {"offset": {"line": 2851, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/AddEditForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PromptItem, ItemType } from '../types';\nimport { Button } from './ui/button';\nimport { Input } from './ui/input';\nimport { Textarea } from './ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';\nimport { Label } from './ui/label';\nimport { Card, CardContent, CardHeader, CardTitle } from './ui/card';\nimport { Badge } from './ui/badge';\nimport { X, Save, FileText, Terminal, Code, Hash, Star, Database, GitBranch, Container, Component, Bot, Settings } from 'lucide-react';\nimport { mockCategories } from '../data/mockData';\n\ninterface AddEditFormProps {\n  item?: PromptItem;\n  onSave: (item: Omit<PromptItem, 'id' | 'dateAdded' | 'lastModified'>) => void;\n  onCancel: () => void;\n}\n\nconst typeIcons = {\n  prompt: FileText,\n  command: Terminal,\n  snippet: Code,\n};\n\nconst availableIcons = [\n  { name: 'FileText', icon: FileText, value: 'FileText' },\n  { name: 'Terminal', icon: Terminal, value: 'Terminal' },\n  { name: 'Code', icon: Code, value: 'Code' },\n  { name: 'Hash', icon: Hash, value: 'Hash' },\n  { name: 'Star', icon: Star, value: 'Star' },\n  { name: 'Database', icon: Database, value: 'Database' },\n  { name: 'GitBranch', icon: GitBranch, value: 'GitBranch' },\n  { name: 'Container', icon: Container, value: 'Container' },\n  { name: 'Component', icon: Component, value: 'Component' },\n  { name: 'Bot', icon: Bot, value: 'Bot' },\n  { name: 'Settings', icon: Settings, value: 'Settings' },\n];\n\nexport function AddEditForm({ item, onSave, onCancel }: AddEditFormProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    type: 'prompt' as ItemType,\n    content: '',\n    description: '',\n    category: '',\n    icon: 'FileText',\n    isFavorite: false,\n  });\n\n  useEffect(() => {\n    if (item) {\n      setFormData({\n        name: item.name,\n        type: item.type,\n        content: item.content,\n        description: item.description || '',\n        category: item.category,\n        icon: item.icon,\n        isFavorite: item.isFavorite,\n      });\n    }\n  }, [item]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!formData.name.trim() || !formData.content.trim() || !formData.category) {\n      return;\n    }\n    onSave(formData);\n  };\n\n  const getSelectedIcon = () => {\n    const iconData = availableIcons.find(i => i.value === formData.icon);\n    return iconData ? iconData.icon : FileText;\n  };\n\n  const SelectedIcon = getSelectedIcon();\n\n  return (\n    <div className=\"max-w-2xl mx-auto\">\n      <Card>\n        <CardHeader className=\"bg-gradient-to-r from-blue-50/50 to-purple-50/50\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <div className=\"p-1 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600\">\n              <SelectedIcon className=\"h-5 w-5 text-white\" />\n            </div>\n            <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n              {item ? 'Edit Item' : 'Add New Item'}\n            </span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Name */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"name\">Name *</Label>\n              <Input\n                id=\"name\"\n                value={formData.name}\n                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                placeholder=\"Enter a descriptive name\"\n                required\n              />\n            </div>\n\n            {/* Type */}\n            <div className=\"space-y-2\">\n              <Label>Type *</Label>\n              <Select value={formData.type} onValueChange={(value: ItemType) => setFormData(prev => ({ ...prev, type: value }))}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"prompt\">\n                    <div className=\"flex items-center gap-2\">\n                      <FileText className=\"h-4 w-4\" />\n                      Prompt\n                    </div>\n                  </SelectItem>\n                  <SelectItem value=\"command\">\n                    <div className=\"flex items-center gap-2\">\n                      <Terminal className=\"h-4 w-4\" />\n                      Command\n                    </div>\n                  </SelectItem>\n                  <SelectItem value=\"snippet\">\n                    <div className=\"flex items-center gap-2\">\n                      <Code className=\"h-4 w-4\" />\n                      Code Snippet\n                    </div>\n                  </SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* Category */}\n            <div className=\"space-y-2\">\n              <Label>Category *</Label>\n              <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select a category\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {mockCategories.map((category) => (\n                    <SelectItem key={category.id} value={category.name}>\n                      {category.name}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* Icon */}\n            <div className=\"space-y-2\">\n              <Label className=\"flex items-center gap-2\">\n                <div className=\"p-1 rounded bg-gradient-to-br from-blue-500 to-purple-600\">\n                  <SelectedIcon className=\"h-3 w-3 text-white\" />\n                </div>\n                Icon *\n              </Label>\n              <Select value={formData.icon} onValueChange={(value) => setFormData(prev => ({ ...prev, icon: value }))}>\n                <SelectTrigger className=\"border-2\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  {availableIcons.map((iconData) => {\n                    const IconComponent = iconData.icon;\n                    return (\n                      <SelectItem key={iconData.value} value={iconData.value}>\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"p-1 rounded bg-gradient-to-br from-blue-500 to-purple-600\">\n                            <IconComponent className=\"h-3 w-3 text-white\" />\n                          </div>\n                          {iconData.name}\n                        </div>\n                      </SelectItem>\n                    );\n                  })}\n                </SelectContent>\n              </Select>\n              <p className=\"text-sm text-muted-foreground\">Choose an icon to represent your {formData.type}</p>\n            </div>\n\n            {/* Description */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Description</Label>\n              <Input\n                id=\"description\"\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                placeholder=\"Optional description\"\n              />\n            </div>\n\n            {/* Content */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"content\">Content *</Label>\n              <Textarea\n                id=\"content\"\n                value={formData.content}\n                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\n                placeholder={`Enter your ${formData.type} content here...`}\n                rows={8}\n                required\n              />\n            </div>\n\n            {/* Favorite */}\n            <div className=\"flex items-center gap-2\">\n              <input\n                type=\"checkbox\"\n                id=\"favorite\"\n                checked={formData.isFavorite}\n                onChange={(e) => setFormData(prev => ({ ...prev, isFavorite: e.target.checked }))}\n                className=\"rounded border border-input\"\n              />\n              <Label htmlFor=\"favorite\" className=\"flex items-center gap-2\">\n                <Star className={`h-4 w-4 ${formData.isFavorite ? 'fill-current text-yellow-500' : ''}`} />\n                Mark as favorite\n              </Label>\n            </div>\n\n            {/* Preview */}\n            <div className=\"space-y-2\">\n              <Label>Preview</Label>\n              <Card className=\"p-4\">\n                <div className=\"flex items-center gap-2 mb-2\">\n                  <div className=\"p-1 rounded bg-gradient-to-br from-blue-500 to-purple-600\">\n                    <SelectedIcon className=\"h-4 w-4 text-white\" />\n                  </div>\n                  <span className=\"font-medium\">{formData.name || 'Item Name'}</span>\n                  <Badge variant=\"secondary\" className={\n                    formData.type === 'prompt' ? 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border border-blue-200' :\n                    formData.type === 'command' ? 'bg-gradient-to-r from-green-100 to-green-50 text-green-800 border border-green-200' :\n                    formData.type === 'snippet' ? 'bg-gradient-to-r from-purple-100 to-purple-50 text-purple-800 border border-purple-200' :\n                    'bg-gray-100 text-gray-800'\n                  }>{formData.type}</Badge>\n                  {formData.category && <Badge variant=\"outline\">{formData.category}</Badge>}\n                  {formData.isFavorite && <Star className=\"h-4 w-4 fill-current text-yellow-500\" />}\n                </div>\n                {formData.description && (\n                  <p className=\"text-muted-foreground text-sm mb-2\">{formData.description}</p>\n                )}\n                <div className=\"bg-muted p-2 rounded text-sm font-mono\">\n                  {formData.content || 'Content will appear here...'}\n                </div>\n              </Card>\n            </div>\n\n            {/* Actions */}\n            <div className=\"flex gap-3\">\n              <Button type=\"submit\" className=\"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\">\n                <Save className=\"h-4 w-4 mr-2\" />\n                {item ? 'Save Changes' : 'Add Item'}\n              </Button>\n              <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n                <X className=\"h-4 w-4 mr-2\" />\n                Cancel\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;;;;;;;AAQA,MAAM,YAAY;IAChB,QAAQ,iNAAA,CAAA,WAAQ;IAChB,SAAS,6MAAA,CAAA,WAAQ;IACjB,SAAS,qMAAA,CAAA,OAAI;AACf;AAEA,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAY,MAAM,iNAAA,CAAA,WAAQ;QAAE,OAAO;IAAW;IACtD;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;QAAE,OAAO;IAAW;IACtD;QAAE,MAAM;QAAQ,MAAM,qMAAA,CAAA,OAAI;QAAE,OAAO;IAAO;IAC1C;QAAE,MAAM;QAAQ,MAAM,qMAAA,CAAA,OAAI;QAAE,OAAO;IAAO;IAC1C;QAAE,MAAM;QAAQ,MAAM,qMAAA,CAAA,OAAI;QAAE,OAAO;IAAO;IAC1C;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;QAAE,OAAO;IAAW;IACtD;QAAE,MAAM;QAAa,MAAM,mNAAA,CAAA,YAAS;QAAE,OAAO;IAAY;IACzD;QAAE,MAAM;QAAa,MAAM,+MAAA,CAAA,YAAS;QAAE,OAAO;IAAY;IACzD;QAAE,MAAM;QAAa,MAAM,+MAAA,CAAA,YAAS;QAAE,OAAO;IAAY;IACzD;QAAE,MAAM;QAAO,MAAM,mMAAA,CAAA,MAAG;QAAE,OAAO;IAAM;IACvC;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;QAAE,OAAO;IAAW;CACvD;AAEM,SAAS,YAAY,KAA4C;QAA5C,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAoB,GAA5C;;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,MAAM;QACN,SAAS;QACT,aAAa;QACb,UAAU;QACV,MAAM;QACN,YAAY;IACd;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,SAAS,KAAK,OAAO;oBACrB,aAAa,KAAK,WAAW,IAAI;oBACjC,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,YAAY,KAAK,UAAU;gBAC7B;YACF;QACF;gCAAG;QAAC;KAAK;IAET,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI,MAAM,CAAC,SAAS,QAAQ,EAAE;YAC3E;QACF;QACA,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,MAAM,WAAW,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,IAAI;QACnE,OAAO,WAAW,SAAS,IAAI,GAAG,iNAAA,CAAA,WAAQ;IAC5C;IAEA,MAAM,eAAe;IAErB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;;8BACH,6LAAC,4HAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC,4HAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAa,WAAU;;;;;;;;;;;0CAE1B,6LAAC;gCAAK,WAAU;0CACb,OAAO,cAAc;;;;;;;;;;;;;;;;;8BAI5B,6LAAC,4HAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,6LAAC,6HAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACvE,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO,SAAS,IAAI;wCAAE,eAAe,CAAC,QAAoB,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM;gDAAM,CAAC;;0DAC7G,6LAAC,8HAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6LAAC,8HAAA,CAAA,gBAAa;;kEACZ,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIpC,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIpC,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAStC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO,SAAS,QAAQ;wCAAE,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU;gDAAM,CAAC;;0DAC3G,6LAAC,8HAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;0DACX,mHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,yBACnB,6LAAC,8HAAA,CAAA,aAAU;wDAAmB,OAAO,SAAS,IAAI;kEAC/C,SAAS,IAAI;uDADC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;0CASpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAa,WAAU;;;;;;;;;;;4CACpB;;;;;;;kDAGR,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO,SAAS,IAAI;wCAAE,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM;gDAAM,CAAC;;0DACnG,6LAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6LAAC,8HAAA,CAAA,gBAAa;0DACX,eAAe,GAAG,CAAC,CAAC;oDACnB,MAAM,gBAAgB,SAAS,IAAI;oDACnC,qBACE,6LAAC,8HAAA,CAAA,aAAU;wDAAsB,OAAO,SAAS,KAAK;kEACpD,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAc,WAAU;;;;;;;;;;;gEAE1B,SAAS,IAAI;;;;;;;uDALD,SAAS,KAAK;;;;;gDASnC;;;;;;;;;;;;kDAGJ,6LAAC;wCAAE,WAAU;;4CAAgC;4CAAkC,SAAS,IAAI;;;;;;;;;;;;;0CAI9F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,6LAAC,6HAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,aAAY;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAU;;;;;;kDACzB,6LAAC,gIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,aAAa,AAAC,cAA2B,OAAd,SAAS,IAAI,EAAC;wCACzC,MAAM;wCACN,QAAQ;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,SAAS,SAAS,UAAU;wCAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,YAAY,EAAE,MAAM,CAAC,OAAO;gDAAC,CAAC;wCAC/E,WAAU;;;;;;kDAEZ,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAW,WAAU;;0DAClC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAAoE,OAA1D,SAAS,UAAU,GAAG,iCAAiC;;;;;;4CAAQ;;;;;;;;;;;;;0CAM/F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC,4HAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAa,WAAU;;;;;;;;;;;kEAE1B,6LAAC;wDAAK,WAAU;kEAAe,SAAS,IAAI,IAAI;;;;;;kEAChD,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WACzB,SAAS,IAAI,KAAK,WAAW,mFAC7B,SAAS,IAAI,KAAK,YAAY,uFAC9B,SAAS,IAAI,KAAK,YAAY,2FAC9B;kEACC,SAAS,IAAI;;;;;;oDACf,SAAS,QAAQ,kBAAI,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,SAAS,QAAQ;;;;;;oDAChE,SAAS,UAAU,kBAAI,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;4CAEzC,SAAS,WAAW,kBACnB,6LAAC;gDAAE,WAAU;0DAAsC,SAAS,WAAW;;;;;;0DAEzE,6LAAC;gDAAI,WAAU;0DACZ,SAAS,OAAO,IAAI;;;;;;;;;;;;;;;;;;0CAM3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,WAAU;;0DAC9B,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,OAAO,iBAAiB;;;;;;;kDAE3B,6LAAC,8HAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;;0DAC/C,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GAnOgB;KAAA", "debugId": null}}, {"offset": {"line": 3636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\nimport { X } from \"lucide-react\";\n\nimport { cn } from \"./utils\";\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className,\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <X />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  );\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAIgC;QAJhC,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD,GAJhC;IAKrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;;;;;0CACF,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3843, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ItemDetailModal.tsx"], "sourcesContent": ["import React from 'react';\nimport { PromptItem } from '../types';\nimport { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from './ui/dialog';\nimport { Button } from './ui/button';\nimport { Badge } from './ui/badge';\nimport { Copy, Edit, Star, Trash2, Calendar } from 'lucide-react';\nimport { toast } from 'sonner';\n\ninterface ItemDetailModalProps {\n  item: PromptItem | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onEdit: (item: PromptItem) => void;\n  onDelete: (id: string) => void;\n  onToggleFavorite: (id: string) => void;\n}\n\nexport function ItemDetailModal({ item, isOpen, onClose, onEdit, onDelete, onToggleFavorite }: ItemDetailModalProps) {\n  if (!item) return null;\n\n  const copyToClipboard = async () => {\n    try {\n      await navigator.clipboard.writeText(item.content);\n      toast.success(`\"${item.name}\" copied to clipboard!`);\n    } catch (err) {\n      toast.error('Failed to copy to clipboard');\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'prompt': return 'bg-blue-100 text-blue-800';\n      case 'command': return 'bg-green-100 text-green-800';\n      case 'snippet': return 'bg-purple-100 text-purple-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatDate = (date: Date) => {\n    return new Intl.DateTimeFormat('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    }).format(date);\n  };\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] flex flex-col\">\n        <DialogHeader>\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex-1 min-w-0\">\n              <DialogTitle className=\"flex items-center gap-3 mb-2\">\n                <span className=\"truncate\">{item.name}</span>\n                {item.isFavorite && <Star className=\"h-5 w-5 fill-current text-yellow-500 flex-shrink-0\" />}\n              </DialogTitle>\n              <div className=\"flex flex-wrap items-center gap-2\">\n                <Badge variant=\"secondary\" className={getTypeColor(item.type)}>\n                  {item.type}\n                </Badge>\n                <Badge variant=\"outline\">{item.category}</Badge>\n                <div className=\"flex items-center gap-1 text-muted-foreground text-sm\">\n                  <Calendar className=\"h-3 w-3\" />\n                  <span>Added {formatDate(item.dateAdded)}</span>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-2 flex-shrink-0 ml-4\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => onToggleFavorite(item.id)}\n              >\n                <Star className={`h-4 w-4 ${item.isFavorite ? 'fill-current text-yellow-500' : ''}`} />\n              </Button>\n              <Button variant=\"ghost\" size=\"sm\" onClick={() => onEdit(item)}>\n                <Edit className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => {\n                  onDelete(item.id);\n                  onClose();\n                }}\n                className=\"text-destructive hover:text-destructive\"\n              >\n                <Trash2 className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </DialogHeader>\n\n        <div className=\"flex-1 min-h-0 space-y-4\">\n          {item.description && (\n            <div>\n              <h4 className=\"font-medium mb-2\">Description</h4>\n              <p className=\"text-muted-foreground\">{item.description}</p>\n            </div>\n          )}\n\n          <div className=\"flex-1 min-h-0\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <h4 className=\"font-medium\">Content</h4>\n              <Button onClick={copyToClipboard} size=\"sm\">\n                <Copy className=\"h-4 w-4 mr-2\" />\n                Copy to Clipboard\n              </Button>\n            </div>\n            <div className=\"bg-muted p-4 rounded-lg h-full min-h-[200px] overflow-auto\">\n              <pre className=\"whitespace-pre-wrap font-mono text-sm leading-relaxed\">\n                {item.content}\n              </pre>\n            </div>\n          </div>\n\n          {item.lastModified.getTime() !== item.dateAdded.getTime() && (\n            <div className=\"text-sm text-muted-foreground\">\n              Last modified: {formatDate(item.lastModified)}\n            </div>\n          )}\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;AAWO,SAAS,gBAAgB,KAAmF;QAAnF,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAwB,GAAnF;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,OAAO;YAChD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,IAAa,OAAV,KAAK,IAAI,EAAC;QAC9B,EAAE,OAAO,KAAK;YACZ,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAK,WAAU;0DAAY,KAAK,IAAI;;;;;;4CACpC,KAAK,UAAU,kBAAI,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;kDAEtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAW,aAAa,KAAK,IAAI;0DACzD,KAAK,IAAI;;;;;;0DAEZ,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW,KAAK,QAAQ;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;;4DAAK;4DAAO,WAAW,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,iBAAiB,KAAK,EAAE;kDAEvC,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAW,AAAC,WAAgE,OAAtD,KAAK,UAAU,GAAG,iCAAiC;;;;;;;;;;;kDAEjF,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,SAAS,IAAM,OAAO;kDACtD,cAAA,6LAAC,8MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,SAAS,KAAK,EAAE;4CAChB;wCACF;wCACA,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM1B,6LAAC;oBAAI,WAAU;;wBACZ,KAAK,WAAW,kBACf,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmB;;;;;;8CACjC,6LAAC;oCAAE,WAAU;8CAAyB,KAAK,WAAW;;;;;;;;;;;;sCAI1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,6LAAC,8HAAA,CAAA,SAAM;4CAAC,SAAS;4CAAiB,MAAK;;8DACrC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAIrC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO;;;;;;;;;;;;;;;;;wBAKlB,KAAK,YAAY,CAAC,OAAO,OAAO,KAAK,SAAS,CAAC,OAAO,oBACrD,6LAAC;4BAAI,WAAU;;gCAAgC;gCAC7B,WAAW,KAAK,YAAY;;;;;;;;;;;;;;;;;;;;;;;;AAO1D;KA5GgB", "debugId": null}}, {"offset": {"line": 4186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/FavoritesPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { PromptItem } from '../types';\nimport { Gallery } from './Gallery';\nimport { Star } from 'lucide-react';\n\ninterface FavoritesPageProps {\n  items: PromptItem[];\n  onEdit: (item: PromptItem) => void;\n  onDelete: (id: string) => void;\n  onToggleFavorite: (id: string) => void;\n  onViewDetails: (item: PromptItem) => void;\n}\n\nexport function FavoritesPage({ items, onEdit, onDelete, onToggleFavorite, onViewDetails }: FavoritesPageProps) {\n  const favoriteItems = items.filter(item => item.isFavorite);\n\n  if (favoriteItems.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <Star className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n        <h2 className=\"mb-2\">No favorites yet</h2>\n        <p className=\"text-muted-foreground\">\n          Star items in your gallery to see them here for quick access.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"p-1 rounded bg-gradient-to-br from-yellow-400 to-orange-500\">\n          <Star className=\"h-5 w-5 text-white fill-current\" />\n        </div>\n        <h1 className=\"bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent\">Favorites</h1>\n      </div>\n      <Gallery\n        items={favoriteItems}\n        onEdit={onEdit}\n        onDelete={onDelete}\n        onToggleFavorite={onToggleFavorite}\n        onViewDetails={onViewDetails}\n      />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;;;AAUO,SAAS,cAAc,KAAgF;QAAhF,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,aAAa,EAAsB,GAAhF;IAC5B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU;IAE1D,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;8BAChB,6LAAC;oBAAG,WAAU;8BAAO;;;;;;8BACrB,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAK3C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAG,WAAU;kCAA+E;;;;;;;;;;;;0BAE/F,6LAAC,yHAAA,CAAA,UAAO;gBACN,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,kBAAkB;gBAClB,eAAe;;;;;;;;;;;;AAIvB;KAhCgB", "debugId": null}}, {"offset": {"line": 4296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/CategoriesPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { PromptItem } from '../types';\nimport { Card, CardContent, CardHeader, CardTitle } from './ui/card';\nimport { Badge } from './ui/badge';\nimport { Tag } from 'lucide-react';\nimport { mockCategories } from '../data/mockData';\n\ninterface CategoriesPageProps {\n  items: PromptItem[];\n}\n\nexport function CategoriesPage({ items }: CategoriesPageProps) {\n  const getCategoryCount = (categoryName: string) => {\n    return items.filter(item => item.category === categoryName).length;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"p-1 rounded bg-gradient-to-br from-blue-500 to-purple-600\">\n          <Tag className=\"h-5 w-5 text-white\" />\n        </div>\n        <h1 className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">Categories</h1>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {mockCategories.map((category) => {\n          const count = getCategoryCount(category.name);\n          return (\n            <Card key={category.id} className=\"hover:shadow-md transition-shadow border-l-4\" style={{ borderLeftColor: category.color }}>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <span className=\"flex items-center gap-2\">\n                    <div \n                      className=\"w-3 h-3 rounded-full\" \n                      style={{ backgroundColor: category.color }}\n                    />\n                    {category.name}\n                  </span>\n                  <Badge variant=\"secondary\" className=\"bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border border-blue-200\">{count}</Badge>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-muted-foreground\">\n                  {count} {count === 1 ? 'item' : 'items'} in this category\n                </p>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;;;;AAMO,SAAS,eAAe,KAA8B;QAA9B,EAAE,KAAK,EAAuB,GAA9B;IAC7B,MAAM,mBAAmB,CAAC;QACxB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,cAAc,MAAM;IACpE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,6LAAC;wBAAG,WAAU;kCAA6E;;;;;;;;;;;;0BAG7F,6LAAC;gBAAI,WAAU;0BACZ,mHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC;oBACnB,MAAM,QAAQ,iBAAiB,SAAS,IAAI;oBAC5C,qBACE,6LAAC,4HAAA,CAAA,OAAI;wBAAmB,WAAU;wBAA+C,OAAO;4BAAE,iBAAiB,SAAS,KAAK;wBAAC;;0CACxH,6LAAC,4HAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,SAAS,KAAK;oDAAC;;;;;;gDAE1C,SAAS,IAAI;;;;;;;sDAEhB,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAkF;;;;;;;;;;;;;;;;;0CAG3H,6LAAC,4HAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAE,WAAU;;wCACV;wCAAM;wCAAE,UAAU,IAAI,SAAS;wCAAQ;;;;;;;;;;;;;uBAfnC,SAAS,EAAE;;;;;gBAoB1B;;;;;;;;;;;;AAIR;KA1CgB", "debugId": null}}, {"offset": {"line": 4452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/switch.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\";\n\nimport { cn } from \"./utils\";\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-switch-background focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className,\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-card dark:data-[state=unchecked]:bg-card-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\",\n        )}\n      />\n    </SwitchPrimitive.Root>\n  );\n}\n\nexport { Switch };\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,KAGoC;QAHpC,EACd,SAAS,EACT,GAAG,OAC+C,GAHpC;IAId,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+WACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,qKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV;KArBS", "debugId": null}}, {"offset": {"line": 4495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/SettingsPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';\nimport { Button } from './ui/button';\nimport { Switch } from './ui/switch';\nimport { Label } from './ui/label';\nimport { Settings, Download, Upload, Moon, Sun, Palette } from 'lucide-react';\n\nexport function SettingsPage() {\n  return (\n    <div className=\"space-y-6 max-w-2xl\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"p-1 rounded bg-gradient-to-br from-gray-500 to-gray-600\">\n          <Settings className=\"h-5 w-5 text-white\" />\n        </div>\n        <h1 className=\"bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent\">Settings</h1>\n      </div>\n\n      {/* Appearance */}\n      <Card className=\"shadow-sm border\">\n        <CardHeader className=\"bg-gradient-to-r from-purple-50/50 to-pink-50/50\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <div className=\"p-1 rounded bg-gradient-to-br from-purple-500 to-pink-600\">\n              <Palette className=\"h-4 w-4 text-white\" />\n            </div>\n            <span className=\"bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\">Appearance</span>\n          </CardTitle>\n          <CardDescription>\n            Customize the look and feel of the application\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-0.5\">\n              <Label className=\"flex items-center gap-2\">\n                <Moon className=\"h-4 w-4 text-purple-600\" />\n                Dark Mode\n              </Label>\n              <p className=\"text-sm text-muted-foreground\">\n                Switch between light and dark themes\n              </p>\n            </div>\n            <Switch />\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-0.5\">\n              <Label className=\"flex items-center gap-2\">\n                <Sun className=\"h-4 w-4 text-orange-500\" />\n                Compact View\n              </Label>\n              <p className=\"text-sm text-muted-foreground\">\n                Show more items in the gallery view\n              </p>\n            </div>\n            <Switch />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Data Management */}\n      <Card className=\"shadow-sm border\">\n        <CardHeader className=\"bg-gradient-to-r from-blue-50/50 to-cyan-50/50\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <div className=\"p-1 rounded bg-gradient-to-br from-blue-500 to-cyan-600\">\n              <Download className=\"h-4 w-4 text-white\" />\n            </div>\n            <span className=\"bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent\">Data Management</span>\n          </CardTitle>\n          <CardDescription>\n            Import and export your prompts and snippets\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" className=\"flex-1 border-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50\">\n              <Upload className=\"h-4 w-4 mr-2\" />\n              Import Data\n            </Button>\n            <Button variant=\"outline\" className=\"flex-1 border-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export Data\n            </Button>\n          </div>\n          <p className=\"text-sm text-muted-foreground\">\n            Support for JSON and CSV formats\n          </p>\n        </CardContent>\n      </Card>\n\n      {/* Preferences */}\n      <Card className=\"shadow-sm border\">\n        <CardHeader className=\"bg-gradient-to-r from-green-50/50 to-emerald-50/50\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <div className=\"p-1 rounded bg-gradient-to-br from-green-500 to-emerald-600\">\n              <Settings className=\"h-4 w-4 text-white\" />\n            </div>\n            <span className=\"bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent\">Preferences</span>\n          </CardTitle>\n          <CardDescription>\n            Configure your default settings\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-0.5\">\n              <Label className=\"flex items-center gap-2\">\n                <div className=\"p-0.5 rounded bg-gradient-to-br from-green-400 to-emerald-500\">\n                  <Download className=\"h-3 w-3 text-white\" />\n                </div>\n                Auto-copy on click\n              </Label>\n              <p className=\"text-sm text-muted-foreground\">\n                Automatically copy content when clicking items\n              </p>\n            </div>\n            <Switch defaultChecked />\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-0.5\">\n              <Label className=\"flex items-center gap-2\">\n                <div className=\"p-0.5 rounded bg-gradient-to-br from-blue-400 to-purple-500\">\n                  <Settings className=\"h-3 w-3 text-white\" />\n                </div>\n                Show toast notifications\n              </Label>\n              <p className=\"text-sm text-muted-foreground\">\n                Display notifications for actions\n              </p>\n            </div>\n            <Switch defaultChecked />\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAEO,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,6LAAC;wBAAG,WAAU;kCAA2E;;;;;;;;;;;;0BAI3F,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6LAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAE/F,6LAAC,4HAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAA4B;;;;;;;0DAG9C,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,6LAAC,8HAAA,CAAA,SAAM;;;;;;;;;;;0CAET,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAA4B;;;;;;;0DAG7C,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,6LAAC,8HAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;0BAMb,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAK,WAAU;kDAA2E;;;;;;;;;;;;0CAE7F,6LAAC,4HAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGrC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAIzC,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;0BAOjD,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAK,WAAU;kDAA+E;;;;;;;;;;;;0CAEjG,6LAAC,4HAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;oDAChB;;;;;;;0DAGR,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,6LAAC,8HAAA,CAAA,SAAM;wCAAC,cAAc;;;;;;;;;;;;0CAExB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;oDAChB;;;;;;;0DAGR,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,6LAAC,8HAAA,CAAA,SAAM;wCAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KA/HgB", "debugId": null}}, {"offset": {"line": 5027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useTheme } from \"next-themes\";\nimport { Toaster as <PERSON><PERSON>, ToasterP<PERSON> } from \"sonner\";\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme();\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU;QAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 5075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Prompt%20Directory%20Application/p/app/page.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from 'react';\nimport { PromptItem } from '../types';\nimport { Layout } from '../components/Layout';\nimport { Gallery } from '../components/Gallery';\nimport { AddEditForm } from '../components/AddEditForm';\nimport { ItemDetailModal } from '../components/ItemDetailModal';\nimport { FavoritesPage } from '../components/FavoritesPage';\nimport { CategoriesPage } from '../components/CategoriesPage';\nimport { SettingsPage } from '../components/SettingsPage';\nimport { SidebarProvider } from '../components/ui/sidebar';\nimport { Toaster } from '../components/ui/sonner';\nimport { mockPrompts } from '../data/mockData';\n\ntype AppMode = 'gallery' | 'add' | 'edit' | 'favorites' | 'categories' | 'settings';\n\nexport default function App() {\n  const [items, setItems] = useState<PromptItem[]>(mockPrompts);\n  const [mode, setMode] = useState<AppMode>('gallery');\n  const [selectedItem, setSelectedItem] = useState<PromptItem | null>(null);\n  const [editingItem, setEditingItem] = useState<PromptItem | null>(null);\n\n  const addItem = (newItem: Omit<PromptItem, 'id' | 'dateAdded' | 'lastModified'>) => {\n    const item: PromptItem = {\n      ...newItem,\n      id: Date.now().toString(),\n      dateAdded: new Date(),\n      lastModified: new Date(),\n    };\n    setItems(prev => [item, ...prev]);\n    setMode('gallery');\n  };\n\n  const updateItem = (updatedItem: Omit<PromptItem, 'id' | 'dateAdded' | 'lastModified'>) => {\n    if (!editingItem) return;\n    \n    setItems(prev => prev.map(item => \n      item.id === editingItem.id \n        ? { \n            ...item, \n            ...updatedItem,\n            lastModified: new Date()\n          }\n        : item\n    ));\n    setEditingItem(null);\n    setMode('gallery');\n  };\n\n  const deleteItem = (id: string) => {\n    setItems(prev => prev.filter(item => item.id !== id));\n    setSelectedItem(null);\n  };\n\n  const toggleFavorite = (id: string) => {\n    setItems(prev => prev.map(item => \n      item.id === id ? { ...item, isFavorite: !item.isFavorite } : item\n    ));\n  };\n\n  const handleEdit = (item: PromptItem) => {\n    setEditingItem(item);\n    setMode('edit');\n  };\n\n  const handleViewDetails = (item: PromptItem) => {\n    setSelectedItem(item);\n  };\n\n  const handleAddNew = () => {\n    setEditingItem(null);\n    setMode('add');\n  };\n\n  const handleCancel = () => {\n    setEditingItem(null);\n    setMode('gallery');\n  };\n\n  const handlePageChange = (page: string) => {\n    setMode(page as AppMode);\n  };\n\n  const renderContent = () => {\n    switch (mode) {\n      case 'add':\n      case 'edit':\n        return (\n          <AddEditForm\n            item={editingItem || undefined}\n            onSave={editingItem ? updateItem : addItem}\n            onCancel={handleCancel}\n          />\n        );\n      case 'favorites':\n        return (\n          <FavoritesPage\n            items={items}\n            onEdit={handleEdit}\n            onDelete={deleteItem}\n            onToggleFavorite={toggleFavorite}\n            onViewDetails={handleViewDetails}\n          />\n        );\n      case 'categories':\n        return <CategoriesPage items={items} />;\n      case 'settings':\n        return <SettingsPage />;\n      default:\n        return (\n          <Gallery\n            items={items}\n            onEdit={handleEdit}\n            onDelete={deleteItem}\n            onToggleFavorite={toggleFavorite}\n            onViewDetails={handleViewDetails}\n          />\n        );\n    }\n  };\n\n  return (\n    <SidebarProvider>\n      <div className=\"min-h-screen w-full\">\n        <Layout\n          currentPage={mode}\n          onPageChange={handlePageChange}\n          onAddNew={handleAddNew}\n        >\n          <div className=\"p-6\">\n            {renderContent()}\n          </div>\n        </Layout>\n        \n        {selectedItem && (\n          <ItemDetailModal\n            item={selectedItem}\n            isOpen={!!selectedItem}\n            onClose={() => setSelectedItem(null)}\n            onEdit={() => handleEdit(selectedItem)}\n            onDelete={() => deleteItem(selectedItem.id)}\n            onToggleFavorite={() => toggleFavorite(selectedItem.id)}\n          />\n        )}\n        \n        <Toaster position=\"top-right\" />\n      </div>\n    </SidebarProvider>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,mHAAA,CAAA,cAAW;IAC5D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAElE,MAAM,UAAU,CAAC;QACf,MAAM,OAAmB;YACvB,GAAG,OAAO;YACV,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI;YACf,cAAc,IAAI;QACpB;QACA,SAAS,CAAA,OAAQ;gBAAC;mBAAS;aAAK;QAChC,QAAQ;IACV;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,aAAa;QAElB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;oBACE,GAAG,IAAI;oBACP,GAAG,WAAW;oBACd,cAAc,IAAI;gBACpB,IACA;QAEN,eAAe;QACf,QAAQ;IACV;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACjD,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,YAAY,CAAC,KAAK,UAAU;gBAAC,IAAI;IAEjE;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,QAAQ;IACV;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,QAAQ;IACV;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,QAAQ;IACV;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ;IACV;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBACE,6LAAC,6HAAA,CAAA,cAAW;oBACV,MAAM,eAAe;oBACrB,QAAQ,cAAc,aAAa;oBACnC,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,6LAAC,+HAAA,CAAA,gBAAa;oBACZ,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,kBAAkB;oBAClB,eAAe;;;;;;YAGrB,KAAK;gBACH,qBAAO,6LAAC,gIAAA,CAAA,iBAAc;oBAAC,OAAO;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,8HAAA,CAAA,eAAY;;;;;YACtB;gBACE,qBACE,6LAAC,yHAAA,CAAA,UAAO;oBACN,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,kBAAkB;oBAClB,eAAe;;;;;;QAGvB;IACF;IAEA,qBACE,6LAAC,+HAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,wHAAA,CAAA,SAAM;oBACL,aAAa;oBACb,cAAc;oBACd,UAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;gBAIJ,8BACC,6LAAC,iIAAA,CAAA,kBAAe;oBACd,MAAM;oBACN,QAAQ,CAAC,CAAC;oBACV,SAAS,IAAM,gBAAgB;oBAC/B,QAAQ,IAAM,WAAW;oBACzB,UAAU,IAAM,WAAW,aAAa,EAAE;oBAC1C,kBAAkB,IAAM,eAAe,aAAa,EAAE;;;;;;8BAI1D,6LAAC,8HAAA,CAAA,UAAO;oBAAC,UAAS;;;;;;;;;;;;;;;;;AAI1B;GArIwB;KAAA", "debugId": null}}]}